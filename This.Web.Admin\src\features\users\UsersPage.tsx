import { Button } from '@/shared/components/atoms/Button/Button';
import { cn } from '@/shared/utils/utils';
import type { ColDef, GridReadyEvent } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { AgGridReact } from 'ag-grid-react';
import { AlertCircle, CheckCircle, RefreshCw, Users, XCircle } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useResponsiveGrid } from '../../hooks/useResponsiveGrid';
import { ApiService, type User } from '../../services/apiService';

interface UsersPageProps {
  className?: string;
}

export const UsersPage: React.FC<UsersPageProps> = ({ className }) => {
  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Responsive grid hook
  const { gridHeight, containerClass, onGridReady: onResponsiveGridReady, isSmallScreen } = useResponsiveGrid({
    minHeight: 300,
    maxHeight: 700,
    headerOffset: 180,
    footerOffset: 80
  });

  // Get tenant ID from localStorage (following existing pattern)
  const getCurrentTenant = (): string => {
    return localStorage.getItem('selectedTenantId') || 'kitchsync'; // fallback to default
  };

  // API service instance
  const apiService = ApiService.getInstance();

  // Fetch users data
  const fetchUsers = useCallback(async (useCache: boolean = true) => {
    try {
      setLoading(true);
      setError(null);
      
      const tenantId = getCurrentTenant();
      const usersData = await apiService.getUsers(tenantId, useCache);
      
      setUsers(usersData);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  }, [apiService]);

  // Initial data load
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    fetchUsers(false); // Force refresh without cache
  }, [fetchUsers]);

  // Combined grid ready handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    // Call the responsive grid handler
    onResponsiveGridReady(params);
  }, [onResponsiveGridReady]);

  // Simple Badge component
  const Badge = ({ children, variant = "default", className = "" }: {
    children: React.ReactNode;
    variant?: "default" | "success" | "destructive" | "secondary" | "outline";
    className?: string;
  }) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium";
    const variantClasses = {
      default: "bg-primary/10 text-primary",
      success: "bg-green-100 text-green-800",
      destructive: "bg-red-100 text-red-800",
      secondary: "bg-gray-100 text-gray-800",
      outline: "border border-gray-300 text-gray-700"
    };

    return (
      <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
        {children}
      </span>
    );
  };

  // Status badge renderer
  const StatusBadgeRenderer = ({ value }: { value: boolean }) => {
    return (
      <Badge
        variant={value ? "success" : "destructive"}
        className="flex items-center gap-1"
      >
        {value ? (
          <>
            <CheckCircle className="h-3 w-3" />
            Active
          </>
        ) : (
          <>
            <XCircle className="h-3 w-3" />
            Inactive
          </>
        )}
      </Badge>
    );
  };

  // Email confirmed badge renderer
  const EmailConfirmedRenderer = ({ value }: { value: boolean }) => {
    return (
      <Badge 
        variant={value ? "success" : "secondary"}
        className="flex items-center gap-1"
      >
        {value ? (
          <>
            <CheckCircle className="h-3 w-3" />
            Confirmed
          </>
        ) : (
          <>
            <AlertCircle className="h-3 w-3" />
            Pending
          </>
        )}
      </Badge>
    );
  };

  // Roles renderer
  const RolesRenderer = ({ value }: { value: string[] }) => {
    if (!value || value.length === 0) {
      return <span className="text-muted-foreground">No roles</span>;
    }
    
    return (
      <div className="flex flex-wrap gap-1">
        {value.map((role, index) => (
          <Badge key={index} variant="outline" className="text-xs">
            {role}
          </Badge>
        ))}
      </div>
    );
  };

  // Date formatter
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // Column definitions with responsive behavior
  const columnDefs: ColDef[] = useMemo(() => {
    const baseColumns: ColDef[] = [
      {
        headerName: 'User Name',
        field: 'userName',
        sortable: true,
        filter: true,
        pinned: isSmallScreen ? false : 'left',
        width: isSmallScreen ? 120 : 150,
        minWidth: 100,
        cellClass: 'font-medium'
      },
      {
        headerName: 'Full Name',
        valueGetter: (params) => `${params.data.firstName} ${params.data.lastName}`,
        sortable: true,
        filter: true,
        width: isSmallScreen ? 140 : 180,
        minWidth: 120
      },
      {
        headerName: 'Email',
        field: 'email',
        sortable: true,
        filter: true,
        width: isSmallScreen ? 160 : 200,
        minWidth: 140
      },
      {
        headerName: 'Status',
        field: 'isActive',
        sortable: true,
        filter: true,
        width: isSmallScreen ? 80 : 100,
        minWidth: 80,
        cellRenderer: StatusBadgeRenderer
      },
      {
        headerName: 'Email Confirmed',
        field: 'emailConfirmed',
        sortable: true,
        filter: true,
        width: isSmallScreen ? 100 : 140,
        minWidth: 100,
        cellRenderer: EmailConfirmedRenderer
      },
      {
        headerName: 'Roles',
        field: 'roles',
        sortable: false,
        filter: false,
        width: isSmallScreen ? 150 : 200,
        minWidth: 120,
        cellRenderer: RolesRenderer
      },
      {
        headerName: 'MFA',
        field: 'isMFAEnabled',
        sortable: true,
        filter: true,
        width: isSmallScreen ? 60 : 120,
        minWidth: 60,
        cellRenderer: ({ value }: { value: boolean }) => (
          <Badge variant={value ? "success" : "secondary"}>
            {isSmallScreen ? (value ? '✓' : '✗') : (value ? 'Yes' : 'No')}
          </Badge>
        )
      }
    ];

    // Add additional columns for larger screens
    if (!isSmallScreen) {
      baseColumns.push(
        {
          headerName: 'Phone',
          field: 'phoneNumber',
          sortable: true,
          filter: true,
          width: 140,
          minWidth: 120
        },
        {
          headerName: 'Created On',
          field: 'createdOn',
          sortable: true,
          filter: true,
          width: 160,
          minWidth: 140,
          valueFormatter: (params) => formatDate(params.value)
        },
        {
          headerName: 'Last Modified',
          field: 'lastModifiedOn',
          sortable: true,
          filter: true,
          width: 160,
          minWidth: 140,
          valueFormatter: (params) => formatDate(params.value)
        }
      );
    }

    return baseColumns;
  }, [isSmallScreen]);

  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Users ({users.length})</h1>
            <p className="text-sm text-muted-foreground">
              Manage and view user accounts
            </p>
          </div>
        </div>
        
        <Button
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-1 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

          <div className={containerClass} style={{ height: `${gridHeight}px`, width: '100%' }}>
            <AgGridReact
              rowData={users}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              loading={loading}

              // Grid configuration
              defaultColDef={{
                sortable: true,
                filter: true,
                resizable: true,
                minWidth: isSmallScreen ? 80 : 100
              }}

              // Pagination
              pagination={true}
              paginationPageSize={isSmallScreen ? 10 : 20}
              paginationPageSizeSelector={isSmallScreen ? [5, 10, 20] : [10, 20, 50, 100]}

              // Other features
              animateRows={true}
              suppressCellFocus={true}
              rowSelection="multiple"

              // Responsive styling
              headerHeight={isSmallScreen ? 34 : 40}
              rowHeight={isSmallScreen ? 30 : 45}

              // Row ID for better performance
              getRowId={(params) => params.data.id}
            />
          </div>
    </div>
  );
};
