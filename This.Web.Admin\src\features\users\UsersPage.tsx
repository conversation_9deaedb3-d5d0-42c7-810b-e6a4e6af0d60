import { Button } from '@/shared/components/atoms/Button/Button';
import { PaginationControls } from '@/shared/components/molecules/PaginationControls/PaginationControls';
import { cn } from '@/shared/utils/utils';
import type { ColDef, GridReadyEvent } from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { AgGridReact } from 'ag-grid-react';
import { AlertCircle, CheckCircle, RefreshCw, Users, XCircle } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useResponsiveGrid } from '../../hooks/useResponsiveGrid';
import { ApiService, type User } from '../../services/apiService';

interface UsersPageProps {
  className?: string;
}

export const UsersPage: React.FC<UsersPageProps> = ({ className }) => {
  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalCount, setTotalCount] = useState(0);
  const [paginatedUsers, setPaginatedUsers] = useState<User[]>([]);

  // Responsive grid hook
  const { gridHeight, containerClass, onGridReady: onResponsiveGridReady, isSmallScreen } = useResponsiveGrid({
    minHeight: 300,
    maxHeight: 700,
    headerOffset: 180,
    footerOffset: 80
  });

  // Get tenant ID from localStorage (following existing pattern)
  const getCurrentTenant = (): string => {
    return localStorage.getItem('selectedTenantId') || 'kitchsync'; // fallback to default
  };

  // API service instance
  const apiService = ApiService.getInstance();

  // Fetch users data
  const fetchUsers = useCallback(async (useCache: boolean = true) => {
    try {
      setLoading(true);
      setError(null);

      const tenantId = getCurrentTenant();
      const usersData = await apiService.getUsers(tenantId, useCache);

      setUsers(usersData);
      setTotalCount(usersData.length);

      // Apply pagination to the data
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = usersData.slice(startIndex, endIndex);
      setPaginatedUsers(paginatedData);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  }, [apiService, currentPage, pageSize]);

  // Initial data load
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    fetchUsers(false); // Force refresh without cache
  }, [fetchUsers]);

  // Pagination handlers
  const handlePageChange = useCallback((newPage: number) => {
    setCurrentPage(newPage);
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when page size changes
  }, []);

  // Update paginated data when page or page size changes
  useEffect(() => {
    if (users.length > 0) {
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = users.slice(startIndex, endIndex);
      setPaginatedUsers(paginatedData);
    }
  }, [users, currentPage, pageSize]);

  // Combined grid ready handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    // Call the responsive grid handler
    onResponsiveGridReady(params);
  }, [onResponsiveGridReady]);

  // Simple Badge component
  const Badge = ({ children, variant = "default", className = "" }: {
    children: React.ReactNode;
    variant?: "default" | "success" | "destructive" | "secondary" | "outline";
    className?: string;
  }) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium";
    const variantClasses = {
      default: "bg-primary/10 text-primary",
      success: "bg-green-100 text-green-800",
      destructive: "bg-red-100 text-red-800",
      secondary: "bg-gray-100 text-gray-800",
      outline: "border border-gray-300 text-gray-700"
    };

    return (
      <span className={`${baseClasses} ${variantClasses[variant]} ${className}`}>
        {children}
      </span>
    );
  };

  // Status badge renderer
  const StatusBadgeRenderer = ({ value }: { value: boolean }) => {
    return (
      <Badge
        variant={value ? "success" : "destructive"}
        className="flex items-center gap-1"
      >
        {value ? (
          <>
            <CheckCircle className="h-3 w-3" />
            Active
          </>
        ) : (
          <>
            <XCircle className="h-3 w-3" />
            Inactive
          </>
        )}
      </Badge>
    );
  };

  // Email confirmed badge renderer
  const EmailConfirmedRenderer = ({ value }: { value: boolean }) => {
    return (
      <Badge 
        variant={value ? "success" : "secondary"}
        className="flex items-center gap-1"
      >
        {value ? (
          <>
            <CheckCircle className="h-3 w-3" />
            Confirmed
          </>
        ) : (
          <>
            <AlertCircle className="h-3 w-3" />
            Pending
          </>
        )}
      </Badge>
    );
  };

  // Roles renderer
  const RolesRenderer = ({ value }: { value: string[] }) => {
    if (!value || value.length === 0) {
      return <span className="text-muted-foreground">No roles</span>;
    }
    
    return (
      <div className="flex flex-wrap gap-1">
        {value.map((role, index) => (
          <Badge key={index} variant="outline" className="text-xs">
            {role}
          </Badge>
        ))}
      </div>
    );
  };

  // Date formatter
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateString;
    }
  };

  // Helper function to format field names as headers
  const formatFieldName = (fieldName: string): string => {
    return fieldName
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
      .trim();
  };

  // Helper function to determine field priority based on importance
  const getFieldPriority = (fieldName: string): number => {
    const priorityMap: Record<string, number> = {
      userName: 1,
      email: 2,
      isActive: 3,
      emailConfirmed: 4,
      roles: 5,
      isMFAEnabled: 6,
      phoneNumber: 7,
      createdOn: 8,
      lastModifiedOn: 9
    };
    return priorityMap[fieldName] || 50; // Default priority for unknown fields
  };

  // Helper function to determine if field should be hidden
  const shouldHideField = (fieldName: string): boolean => {
    // Hide sensitive or internal fields
    const sensitiveFields = ['id', 'otp', 'otpUpdatedOn', 'imageUrl', 'isDeleted',
                           'createdBy', 'lastModifiedBy', 'timeZoneInfo', 'licenseNo'];
    if (sensitiveFields.includes(fieldName)) return true;

    // Hide individual name fields since we have a computed Full Name column
    if (['firstName', 'lastName'].includes(fieldName)) return true;

    // Hide fields on small screens based on priority
    if (isSmallScreen) {
      const lowPriorityFields = ['phoneNumber', 'phoneNumberConfirmed', 'createdOn', 'lastModifiedOn'];
      if (lowPriorityFields.includes(fieldName)) return true;
    }

    return false;
  };

  // Helper function to get appropriate cell renderer based on field name and type
  const getCellRenderer = (fieldName: string, value: any): any => {
    // Boolean status fields
    if (fieldName === 'isActive') return StatusBadgeRenderer;
    if (fieldName === 'emailConfirmed') return EmailConfirmedRenderer;
    if (fieldName === 'isMFAEnabled') {
      return ({ value }: { value: boolean }) => (
        <Badge variant={value ? "success" : "secondary"}>
          {isSmallScreen ? (value ? '✓' : '✗') : (value ? 'Yes' : 'No')}
        </Badge>
      );
    }

    // Array fields
    if (fieldName === 'roles' && Array.isArray(value)) return RolesRenderer;

    // No special renderer needed
    return undefined;
  };

  // Helper function to get value formatter based on field name and type
  const getValueFormatter = (fieldName: string, value: any): any => {
    // Date fields
    if (fieldName.includes('On') || fieldName.includes('At')) {
      if (typeof value === 'string' && value.includes('T')) {
        return (params: any) => formatDate(params.value);
      }
    }
    return undefined;
  };

  // Helper function to get column width based on field name and type
  const getColumnWidth = (fieldName: string, value: any): { width: number; minWidth: number } => {
    // Custom widths for specific fields
    const customWidths: Record<string, { width: number; minWidth: number }> = {
      userName: { width: isSmallScreen ? 120 : 150, minWidth: 100 },
      email: { width: isSmallScreen ? 160 : 200, minWidth: 140 },
      isActive: { width: isSmallScreen ? 80 : 100, minWidth: 80 },
      emailConfirmed: { width: isSmallScreen ? 100 : 140, minWidth: 100 },
      roles: { width: isSmallScreen ? 150 : 200, minWidth: 120 },
      isMFAEnabled: { width: isSmallScreen ? 60 : 120, minWidth: 60 },
      phoneNumber: { width: 140, minWidth: 120 }
    };

    if (customWidths[fieldName]) return customWidths[fieldName];

    // Default widths based on data type
    if (typeof value === 'boolean') {
      return { width: isSmallScreen ? 80 : 100, minWidth: 80 };
    }
    if (fieldName.includes('On') || fieldName.includes('At')) {
      return { width: 160, minWidth: 140 };
    }

    // Default width
    return { width: isSmallScreen ? 120 : 150, minWidth: isSmallScreen ? 80 : 100 };
  };

  // Dynamic column definitions based on actual User data
  const columnDefs: ColDef[] = useMemo(() => {
    if (users.length === 0) return [];

    // Get all unique fields from the user data
    const sampleUser = users[0];
    const allFields = Object.keys(sampleUser);

    // Add Full Name as a computed column first
    const columns: ColDef[] = [{
      headerName: 'Full Name',
      field: 'fullName',
      valueGetter: (params) => `${params.data.firstName} ${params.data.lastName}`,
      sortable: true,
      filter: true,
      width: isSmallScreen ? 140 : 180,
      minWidth: 120
    }];

    // Generate columns for each field automatically
    allFields.forEach(field => {
      const sampleValue = sampleUser[field as keyof User];

      // Skip hidden fields
      if (shouldHideField(field)) return;

      const { width, minWidth } = getColumnWidth(field, sampleValue);
      const cellRenderer = getCellRenderer(field, sampleValue);
      const valueFormatter = getValueFormatter(field, sampleValue);

      const colDef: ColDef = {
        headerName: formatFieldName(field),
        field: field,
        sortable: true,
        filter: true,
        width,
        minWidth,
        cellRenderer,
        valueFormatter
      };

      // Special configurations
      if (field === 'userName') {
        colDef.pinned = isSmallScreen ? false : 'left';
        colDef.cellClass = 'font-medium';
      }

      if (field === 'roles') {
        colDef.sortable = false;
        colDef.filter = false;
      }

      columns.push(colDef);
    });

    // Sort columns by priority
    return columns.sort((a, b) => {
      // Full Name always comes first
      if (a.field === 'fullName') return -1;
      if (b.field === 'fullName') return 1;

      const aPriority = getFieldPriority(a.field || '');
      const bPriority = getFieldPriority(b.field || '');
      return aPriority - bPriority;
    });
  }, [users, isSmallScreen]);

  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-foreground">Users ({users.length})</h1>
            <p className="text-sm text-muted-foreground">
              Manage and view user accounts
            </p>
          </div>
        </div>
        
        <Button
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-1 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

          <div className="space-y-4">
            <div className={containerClass} style={{ height: `${gridHeight}px`, width: '100%' }}>
              <AgGridReact
                rowData={paginatedUsers}
                columnDefs={columnDefs}
                onGridReady={onGridReady}
                loading={loading}

                // Grid configuration
                defaultColDef={{
                  sortable: true,
                  filter: true,
                  resizable: true,
                  minWidth: isSmallScreen ? 80 : 100
                }}

                // Disable built-in pagination
                pagination={false}

                // Other features
                animateRows={true}
                suppressCellFocus={true}
                rowSelection="multiple"

                // Responsive styling
                headerHeight={isSmallScreen ? 34 : 40}
                rowHeight={isSmallScreen ? 30 : 45}

                // Row ID for better performance
                getRowId={(params) => params.data.id}
              />
            </div>

            {/* Custom Pagination Controls */}
            <PaginationControls
              totalCount={totalCount}
              currentPage={currentPage}
              pageSize={pageSize}
              loading={loading}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          </div>
    </div>
  );
};
