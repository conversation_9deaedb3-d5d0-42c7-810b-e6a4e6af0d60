import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { routes } from '../config/routes';

interface DynamicTitleResult {
  title: string;
  isLoading: boolean;
  error: string | null;
}

/**
 * Custom hook for dynamic title generation based on current route
 * Handles object detail and upsert pages
 */
export const useDynamicTitle = (): DynamicTitleResult => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const [title, setTitle] = useState<string>('Application Settings');
  const [isLoading, _setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const generateTitle = () => {
      try {
        // Check for upsert route: /object/:objectName/upsert?refId=:id
        const upsertMatch = location.pathname.match(/^\/object\/([^/]+)\/(?:upsert|new|edit)/);

        if (upsertMatch) {
          const isEditMode = !!searchParams.get('refId');
          const objectName = decodeURIComponent(upsertMatch[1]);
          setTitle(`${isEditMode ? 'Edit' : 'New'} ${objectName}`);
          return;
        }

        // Check for object detail route: /object/:objectName
        const objectMatch = location.pathname.match(/^\/object\/([^/]+)$/);
        if (objectMatch) {
          const objectName = decodeURIComponent(objectMatch[1]);
          setTitle(objectName);
          return;
        }

        // Check for static routes from routes configuration
        const currentRoute = routes.find(route => route.path === location.pathname);
        if (currentRoute) {
          setTitle(currentRoute.title);
          return;
        }

        // Default title for other routes
        setTitle('Application Settings');
      } catch (err) {
        console.error('Error generating title:', err);
        setError('Failed to load page title');
        setTitle('Error');
      }
    };

    generateTitle();
  }, [location.pathname, location.search]);

  // Update document title for browser tab
  useEffect(() => {
    if (title && !isLoading && !error) {
      document.title = `${title} - Admin`;
    }
  }, [title, isLoading, error]);

  return { title, isLoading, error };
};

/**
 * Hook to get object name from URL
 */
export const useObjectName = (): string | null => {
  const location = useLocation();
  const objectRouteMatch = location.pathname.match(/^\/object\/(.+)$/);
  return objectRouteMatch ? decodeURIComponent(objectRouteMatch[1]) : null;
};
